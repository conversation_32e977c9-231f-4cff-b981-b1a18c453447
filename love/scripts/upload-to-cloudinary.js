#!/usr/bin/env node

/**
 * Cloudinary视频上传脚本
 * 将本地视频文件批量上传到Cloudinary
 * 
 * 使用前请先安装依赖：npm install cloudinary
 * 设置环境变量：
 * export CLOUDINARY_CLOUD_NAME=your-cloud-name
 * export CLOUDINARY_API_KEY=your-api-key  
 * export CLOUDINARY_API_SECRET=your-api-secret
 */

const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');

// 配置Cloudinary
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// 视频文件映射
const videoMappings = [
    {
        localPath: 'background/cloudinary-ready/home.mp4',
        publicId: 'love-website/home',
        folder: 'love-website'
    },
    {
        localPath: 'background/cloudinary-ready/meetings.mp4', 
        publicId: 'love-website/meetings',
        folder: 'love-website'
    },
    {
        localPath: 'background/cloudinary-ready/anniversary.mp4',
        publicId: 'love-website/anniversary', 
        folder: 'love-website'
    },
    {
        localPath: 'background/cloudinary-ready/together-days.mp4',
        publicId: 'love-website/together-days',
        folder: 'love-website'
    },
    {
        localPath: 'background/cloudinary-ready/memorial.mp4',
        publicId: 'love-website/memorial',
        folder: 'love-website'
    }
];

// 上传单个视频文件
async function uploadVideo(mapping) {
    const { localPath, publicId, folder } = mapping;
    
    console.log(`📤 开始上传: ${localPath}`);
    
    try {
        // 检查文件是否存在
        if (!fs.existsSync(localPath)) {
            console.error(`❌ 文件不存在: ${localPath}`);
            return false;
        }

        // 检查文件大小
        const stats = fs.statSync(localPath);
        const fileSizeMB = stats.size / (1024 * 1024);
        console.log(`📊 文件大小: ${fileSizeMB.toFixed(2)}MB`);
        
        if (fileSizeMB > 100) {
            console.error(`❌ 文件过大 (${fileSizeMB.toFixed(2)}MB > 100MB)，请先压缩`);
            return false;
        }

        // 上传配置 - 简化版本
        const uploadOptions = {
            public_id: publicId,
            resource_type: 'video',
            overwrite: true,
            // 添加标签便于管理
            tags: ['love-website', 'background-video'],
            // 生成缩略图
            eager: [
                { width: 400, height: 225, crop: 'fill', format: 'jpg' }
            ]
        };

        const result = await cloudinary.uploader.upload(localPath, uploadOptions);
        
        console.log(`✅ 上传成功: ${publicId}`);
        console.log(`🔗 URL: ${result.secure_url}`);
        console.log(`📏 尺寸: ${result.width}x${result.height}`);
        console.log(`⏱️  时长: ${result.duration}秒`);
        console.log('---');
        
        return true;
        
    } catch (error) {
        console.error(`❌ 上传失败: ${publicId}`, error.message);
        return false;
    }
}

// 批量上传所有视频
async function uploadAllVideos() {
    console.log('🚀 开始批量上传视频到Cloudinary...\n');
    
    // 验证环境变量
    if (!process.env.CLOUDINARY_CLOUD_NAME || 
        !process.env.CLOUDINARY_API_KEY || 
        !process.env.CLOUDINARY_API_SECRET) {
        console.error('❌ 请设置Cloudinary环境变量:');
        console.error('export CLOUDINARY_CLOUD_NAME=your-cloud-name');
        console.error('export CLOUDINARY_API_KEY=your-api-key');
        console.error('export CLOUDINARY_API_SECRET=your-api-secret');
        process.exit(1);
    }

    let successCount = 0;
    let totalCount = videoMappings.length;

    for (const mapping of videoMappings) {
        const success = await uploadVideo(mapping);
        if (success) successCount++;
        
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`\n📊 上传完成统计:`);
    console.log(`✅ 成功: ${successCount}/${totalCount}`);
    console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 所有视频上传成功！');
        console.log('💡 下一步：在config.js中设置CLOUDINARY.ENABLED = true');
    }
}

// 执行上传
if (require.main === module) {
    uploadAllVideos().catch(console.error);
}

module.exports = { uploadVideo, uploadAllVideos };
