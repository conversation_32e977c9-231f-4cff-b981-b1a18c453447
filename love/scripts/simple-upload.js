#!/usr/bin/env node

/**
 * 简化的Cloudinary上传脚本
 */

const cloudinary = require('cloudinary').v2;
const fs = require('fs');

// 配置Cloudinary
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// 简化的视频映射
const videos = [
    { file: 'background/cloudinary-ready/home.mp4', id: 'love-website/home' },
    { file: 'background/cloudinary-ready/meetings.mp4', id: 'love-website/meetings' },
    { file: 'background/cloudinary-ready/memorial.mp4', id: 'love-website/memorial' },
    { file: 'background/cloudinary-ready/together-days.mp4', id: 'love-website/together-days' },
    { file: 'background/cloudinary-ready/anniversary.mp4', id: 'love-website/anniversary' }
];

async function uploadVideo(video) {
    console.log(`📤 上传: ${video.file}`);
    
    try {
        if (!fs.existsSync(video.file)) {
            console.error(`❌ 文件不存在: ${video.file}`);
            return false;
        }

        const result = await cloudinary.uploader.upload(video.file, {
            public_id: video.id,
            resource_type: 'video',
            overwrite: true
        });
        
        console.log(`✅ 上传成功: ${video.id}`);
        console.log(`🔗 URL: ${result.secure_url}`);
        console.log('---');
        return true;
        
    } catch (error) {
        console.error(`❌ 上传失败: ${video.id}`, error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 开始简化上传...\n');
    
    let success = 0;
    for (const video of videos) {
        if (await uploadVideo(video)) {
            success++;
        }
        // 延迟1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n📊 上传结果: ${success}/${videos.length} 成功`);
}

main().catch(console.error);
