#!/usr/bin/env node

const cloudinary = require('cloudinary').v2;

// 直接配置（用于测试）
cloudinary.config({
    cloud_name: 'dcglebe2w',
    api_key: '899226968246286',
    api_secret: 'FhvmlQJX_OLQszwF6YF9KnhmoU'
});

async function testUpload() {
    try {
        console.log('🧪 测试上传最小的视频文件...');
        
        const result = await cloudinary.uploader.upload('background/cloudinary-ready/meetings.mp4', {
            resource_type: 'video',
            public_id: 'test-upload'
        });
        
        console.log('✅ 测试上传成功!');
        console.log('URL:', result.secure_url);
        
        // 删除测试文件
        await cloudinary.uploader.destroy('test-upload', { resource_type: 'video' });
        console.log('🗑️ 测试文件已清理');
        
    } catch (error) {
        console.error('❌ 测试上传失败:', error.message);
        if (error.http_code) {
            console.error('HTTP状态码:', error.http_code);
        }
    }
}

testUpload();
