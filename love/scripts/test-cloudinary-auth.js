#!/usr/bin/env node

/**
 * 测试Cloudinary认证
 */

const cloudinary = require('cloudinary').v2;

// 配置Cloudinary
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

async function testAuth() {
    console.log('🔧 测试Cloudinary认证...');
    console.log(`Cloud Name: ${process.env.CLOUDINARY_CLOUD_NAME}`);
    console.log(`API Key: ${process.env.CLOUDINARY_API_KEY}`);
    console.log(`API Secret: ${process.env.CLOUDINARY_API_SECRET ? '[已设置]' : '[未设置]'}`);
    
    try {
        // 测试API连接
        const result = await cloudinary.api.ping();
        console.log('✅ Cloudinary连接成功:', result);
        
        // 获取账户信息
        const usage = await cloudinary.api.usage();
        console.log('📊 账户使用情况:', {
            credits: usage.credits,
            bandwidth: usage.bandwidth,
            storage: usage.storage
        });
        
    } catch (error) {
        console.error('❌ Cloudinary认证失败:', error.message);
        console.error('详细错误:', error);
    }
}

testAuth();
