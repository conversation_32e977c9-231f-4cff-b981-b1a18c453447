#!/bin/bash

# 先处理小文件的压缩脚本
# 优先处理符合免费计划限制的视频文件

echo "🎬 开始处理小视频文件..."

# 创建输出目录
mkdir -p background/cloudinary-ready

echo "📁 复制已符合大小要求的视频文件..."

# 直接复制符合要求的文件（<100MB）
cp background/home/<USER>/cloudinary-ready/ 2>/dev/null && echo "✅ home.mp4 复制完成 (63MB)"
cp background/meetings/meetings.mp4 background/cloudinary-ready/ 2>/dev/null && echo "✅ meetings.mp4 复制完成 (39MB)"  
cp background/memorial/memorial.mp4 background/cloudinary-ready/ 2>/dev/null && echo "✅ memorial.mp4 复制完成 (93MB)"

# 压缩together-days.mp4 (146MB -> <100MB) - 快速压缩
echo "📹 快速压缩together-days.mp4..."
if [ -f "background/together-days/together-days.mp4" ]; then
    ffmpeg -i background/together-days/together-days.mp4 \
        -c:v libx264 \
        -crf 24 \
        -preset fast \
        -c:a aac \
        -b:a 128k \
        -vf "scale=1920:1080:flags=lanczos" \
        -movflags +faststart \
        -y \
        background/cloudinary-ready/together-days.mp4
    echo "✅ together-days.mp4 压缩完成"
else
    echo "❌ together-days.mp4 文件不存在"
fi

# 检查文件大小
echo "📊 处理后文件大小："
if [ -d "background/cloudinary-ready" ]; then
    ls -lh background/cloudinary-ready/*.mp4 2>/dev/null || echo "没有找到处理后的文件"
fi

echo "✅ 小文件处理完成！"
echo "💡 这些文件可以直接上传到Cloudinary免费计划"
echo "⚠️  anniversary.mp4 (570MB) 需要单独处理或考虑付费计划"
