/**
 * Cloudinary多API负载均衡管理器
 * 支持多个Cloudinary账号，智能分发请求
 */

class CloudinaryLoadBalancer {
    constructor() {
        // 多个Cloudinary配置
        this.providers = [
            {
                id: 'primary',
                cloudName: 'dcglebc2w',
                baseUrl: 'https://res.cloudinary.com/dcglebc2w',
                weight: 50, // 权重百分比
                status: 'active',
                errorCount: 0,
                lastError: null
            },
            {
                id: 'secondary', 
                cloudName: 'your-second-cloud-name', // 需要第二个账号
                baseUrl: 'https://res.cloudinary.com/your-second-cloud-name',
                weight: 50,
                status: 'active', 
                errorCount: 0,
                lastError: null
            }
        ];

        // 视频配置映射
        this.videoConfigs = {
            'home': {
                publicId: 'love-website/home',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'meetings': {
                publicId: 'love-website/meetings',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'anniversary': {
                publicId: 'love-website/anniversary',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'together-days': {
                publicId: 'love-website/together-days',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'memorial': {
                publicId: 'love-website/memorial',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            }
        };

        // 负载均衡策略
        this.strategy = 'round-robin'; // 'round-robin', 'weighted', 'failover'
        this.currentIndex = 0;
    }

    // 获取可用的提供商
    getAvailableProviders() {
        return this.providers.filter(p => p.status === 'active');
    }

    // 选择提供商（轮询策略）
    selectProvider() {
        const available = this.getAvailableProviders();
        if (available.length === 0) {
            console.error('❌ 没有可用的Cloudinary提供商');
            return null;
        }

        let selected;
        switch (this.strategy) {
            case 'round-robin':
                selected = available[this.currentIndex % available.length];
                this.currentIndex++;
                break;
            
            case 'weighted':
                selected = this.selectByWeight(available);
                break;
                
            case 'failover':
                selected = available[0]; // 总是选择第一个可用的
                break;
                
            default:
                selected = available[0];
        }

        console.log(`🔄 选择提供商: ${selected.id} (${selected.cloudName})`);
        return selected;
    }

    // 按权重选择
    selectByWeight(providers) {
        const totalWeight = providers.reduce((sum, p) => sum + p.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const provider of providers) {
            random -= provider.weight;
            if (random <= 0) {
                return provider;
            }
        }
        return providers[0];
    }

    // 生成视频URL
    generateVideoUrl(pageName, provider = null) {
        const config = this.videoConfigs[pageName];
        if (!config) {
            console.error(`未找到页面 ${pageName} 的视频配置`);
            return null;
        }

        // 如果没有指定提供商，自动选择
        if (!provider) {
            provider = this.selectProvider();
            if (!provider) return null;
        }

        // 设备优化
        const deviceTransforms = this.getDeviceOptimizations();
        const finalTransforms = `${config.transformations},${deviceTransforms}`;
        
        return `${provider.baseUrl}/video/upload/${finalTransforms}/${config.publicId}.mp4`;
    }

    // 设备优化策略
    getDeviceOptimizations() {
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        const isSlowConnection = navigator.connection && 
            (navigator.connection.effectiveType === 'slow-2g' || 
             navigator.connection.effectiveType === '2g');

        if (isSlowConnection) {
            return 'q_auto:eco,w_1280,h_720';
        }
        
        if (isMobile) {
            return 'q_auto:good,w_1920,h_1080';
        }
        
        if (isTablet) {
            return 'q_auto:best,w_2048,h_1152';
        }
        
        return 'q_auto:best,w_2560,h_1440';
    }

    // 智能加载视频（带故障转移）
    async loadVideoBackground(pageName, videoElement) {
        const maxRetries = this.getAvailableProviders().length;
        let attempts = 0;

        while (attempts < maxRetries) {
            const provider = this.selectProvider();
            if (!provider) break;

            try {
                const videoUrl = this.generateVideoUrl(pageName, provider);
                if (!videoUrl) continue;

                console.log(`🎬 尝试加载视频: ${pageName} from ${provider.id}`);
                
                // 设置加载超时
                const loadPromise = new Promise((resolve, reject) => {
                    videoElement.addEventListener('canplaythrough', resolve, { once: true });
                    videoElement.addEventListener('error', reject, { once: true });
                    setTimeout(() => reject(new Error('加载超时')), 10000);
                });

                videoElement.src = videoUrl;
                videoElement.load();

                await loadPromise;

                // 成功加载
                videoElement.style.opacity = '1';
                videoElement.play().catch(e => {
                    console.log('自动播放被阻止，等待用户交互');
                });

                // 重置错误计数
                provider.errorCount = 0;
                console.log(`✅ 视频加载成功: ${pageName} from ${provider.id}`);
                return true;

            } catch (error) {
                console.error(`❌ 提供商 ${provider.id} 加载失败:`, error.message);
                
                // 记录错误
                provider.errorCount++;
                provider.lastError = new Date();
                
                // 如果错误次数过多，暂时禁用
                if (provider.errorCount >= 3) {
                    provider.status = 'disabled';
                    console.warn(`⚠️ 提供商 ${provider.id} 已被暂时禁用`);
                    
                    // 5分钟后重新启用
                    setTimeout(() => {
                        provider.status = 'active';
                        provider.errorCount = 0;
                        console.log(`🔄 提供商 ${provider.id} 已重新启用`);
                    }, 5 * 60 * 1000);
                }
                
                attempts++;
            }
        }

        // 所有提供商都失败，回退到渐变背景
        console.error(`❌ 所有Cloudinary提供商都失败，回退到渐变背景`);
        this.fallbackToGradient(videoElement);
        return false;
    }

    // 失败时回退到渐变背景
    fallbackToGradient(videoElement) {
        const container = videoElement.parentElement;
        if (container) {
            container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 获取负载均衡状态
    getStatus() {
        return {
            strategy: this.strategy,
            providers: this.providers.map(p => ({
                id: p.id,
                status: p.status,
                errorCount: p.errorCount,
                weight: p.weight
            })),
            currentIndex: this.currentIndex
        };
    }
}

// 全局初始化
window.CloudinaryLoadBalancer = CloudinaryLoadBalancer;
