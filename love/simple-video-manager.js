/**
 * Simple Video Manager - 简单直接的视频管理器
 * 完全删除复杂优化策略，直接加载视频
 */

class SimpleVideoManager {
    constructor() {
        console.log('🚀 Simple Video Manager initialized - Enhanced with CDN Load Balancing');
        this.currentVideo = null;

        // 初始化CDN管理器
        this.initializeCDNManagers();
    }

    // 初始化CDN管理器
    initializeCDNManagers() {
        // 检查是否启用Cloudinary
        this.cloudinaryEnabled = window.CONFIG?.CLOUDINARY?.ENABLED || false;

        // 优先使用混合CDN管理器
        if (window.HybridCDNManager) {
            this.cdnManager = new window.HybridCDNManager();
            console.log('🔄 混合CDN管理器已启用');
            return;
        }

        // 备选：负载均衡器
        if (window.CloudinaryLoadBalancer && this.cloudinaryEnabled) {
            this.cdnManager = new window.CloudinaryLoadBalancer();
            console.log('⚖️ Cloudinary负载均衡器已启用');
            return;
        }

        // 备选：原始Cloudinary管理器
        if (window.CloudinaryVideoManager && this.cloudinaryEnabled) {
            this.cdnManager = new window.CloudinaryVideoManager(
                window.CONFIG.CLOUDINARY.CLOUD_NAME
            );
            console.log('☁️ 基础Cloudinary管理器已启用');
            return;
        }

        console.log('📁 使用本地文件模式');
        this.cdnManager = null;
    }

    /**
     * 智能加载视频 - 支持多CDN负载均衡
     */
    async loadVideo(pageKey, videoConfig) {
        console.log(`🎬 Loading video for ${pageKey}:`, videoConfig.name);

        try {
            // 创建视频元素
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';

            // 使用CDN管理器（如果可用）
            if (this.cdnManager) {
                console.log(`🔄 Using CDN Manager for ${pageKey}`);

                // 分析用户上下文
                const userContext = this.getUserContext();

                let result;
                if (this.cdnManager.loadVideoBackground) {
                    // 混合CDN管理器或负载均衡器
                    result = await this.cdnManager.loadVideoBackground(pageKey, video, userContext);
                } else if (this.cdnManager.loadVideo) {
                    // 原始Cloudinary管理器
                    result = await this.cdnManager.loadVideo(pageKey, videoConfig);
                }

                if (result === true || (result && result.success)) {
                    this.currentVideo = video;
                    console.log(`✅ CDN video loaded successfully for ${pageKey}`);
                    return video;
                }
                console.log(`⚠️ CDN loading failed, falling back to local file`);
            }

            // 回退到本地文件
            console.log(`📁 Using local file: ${videoConfig.url}`);
            video.src = videoConfig.url;

            // 等待视频加载
            await this.waitForVideoLoad(video);

            this.currentVideo = video;
            console.log(`✅ Local video loaded successfully for ${pageKey}`);
            return video;

        } catch (error) {
            console.error(`❌ Failed to load video for ${pageKey}:`, error);
            throw error;
        }
    }

    /**
     * 获取用户上下文信息
     */
    getUserContext() {
        return {
            isMobile: window.innerWidth <= 768,
            isTablet: window.innerWidth > 768 && window.innerWidth <= 1024,
            isSlowConnection: navigator.connection &&
                (navigator.connection.effectiveType === 'slow-2g' ||
                 navigator.connection.effectiveType === '2g'),
            userAgent: navigator.userAgent,
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio || 1
        };
    }

    /**
     * 等待视频加载完成
     */
    waitForVideoLoad(video) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Video load timeout after 30 seconds'));
            }, 30000);

            video.addEventListener('loadeddata', () => {
                clearTimeout(timeout);
                console.log('✅ Video loadeddata event fired');
                resolve(video);
            });

            video.addEventListener('error', (e) => {
                clearTimeout(timeout);
                console.error('❌ Video error event:', e);
                reject(new Error(`Video load error: ${e.message || 'Unknown error'}`));
            });

            // 开始加载
            video.load();
        });
    }

    /**
     * 预加载视频（简化版）
     */
    async preloadVideo(pageKey, videoConfig) {
        console.log(`🔄 Preloading video for ${pageKey}:`, videoConfig.name);
        try {
            await this.loadVideo(pageKey, videoConfig);
        } catch (error) {
            console.log(`⚠️ Preload failed for ${pageKey}:`, error);
        }
    }

    /**
     * 应用备用背景
     */
    applyFallbackBackground(theme) {
        console.log(`🎨 Applying fallback background: ${theme}`);
        
        const fallbackConfig = window.CONFIG?.VIDEOS?.FALLBACK_THEMES?.[theme];
        if (!fallbackConfig) {
            console.error(`❌ Fallback theme not found: ${theme}`);
            return;
        }

        const videoContainer = document.querySelector('.video-background');
        if (videoContainer) {
            videoContainer.style.background = fallbackConfig.gradient;
            videoContainer.style.animation = fallbackConfig.animation;
            videoContainer.classList.add('fallback-active');
        }
    }

    /**
     * 暂停所有视频
     */
    pauseAllVideos() {
        if (this.currentVideo && !this.currentVideo.paused) {
            this.currentVideo.pause();
            console.log('⏸️ Paused current video');
        }
    }

    /**
     * 恢复当前视频
     */
    resumeCurrentVideo() {
        if (this.currentVideo && this.currentVideo.paused) {
            this.currentVideo.play().catch(e => {
                console.log('⚠️ Failed to resume video:', e);
            });
            console.log('▶️ Resumed current video');
        }
    }

    /**
     * 获取缓存状态
     */
    getCacheStatus() {
        return {
            currentVideo: this.currentVideo ? 'loaded' : 'none',
            videoSrc: this.currentVideo ? this.currentVideo.src : 'none'
        };
    }

    /**
     * 获取当前页面键（兼容方法）
     */
    getCurrentPageKey() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'INDEX';
        if (path === '/meetings' || path === '/meetings.html') return 'MEETINGS';
        if (path === '/anniversary' || path === '/anniversary.html') return 'ANNIVERSARY';
        if (path === '/memorial' || path === '/memorial.html') return 'MEMORIAL';
        if (path === '/together-days' || path === '/together-days.html') return 'TOGETHER_DAYS';
        return 'INDEX';
    }
}

// 创建全局实例
window.VideoManager = new SimpleVideoManager();

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleVideoManager;
}

console.log('📦 Simple Video Manager loaded and ready');
