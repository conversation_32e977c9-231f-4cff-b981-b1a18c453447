<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudinary性能对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-test {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-item {
            flex: 1;
            text-align: center;
        }
        .video-item video {
            width: 100%;
            max-width: 400px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .metrics {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .metric-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.loading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover { background: #0056b3; }
        .results {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 Cloudinary vs 本地文件性能对比测试</h1>
    
    <div class="test-container">
        <h2>📊 测试说明</h2>
        <p>此测试将对比Cloudinary CDN和本地文件的加载性能，包括：</p>
        <ul>
            <li>加载时间对比</li>
            <li>文件大小对比</li>
            <li>网络传输效率</li>
            <li>用户体验差异</li>
        </ul>
    </div>

    <div class="controls">
        <button onclick="startTest()">🎬 开始性能测试</button>
        <button onclick="clearResults()">🗑️ 清除结果</button>
    </div>

    <div class="video-test">
        <div class="video-item">
            <h3>☁️ Cloudinary CDN</h3>
            <video id="cloudinary-video" muted loop></video>
            <div class="metrics">
                <div class="metric-item">
                    <span>状态:</span>
                    <span id="cloudinary-status" class="status">待测试</span>
                </div>
                <div class="metric-item">
                    <span>加载时间:</span>
                    <span id="cloudinary-time">-</span>
                </div>
                <div class="metric-item">
                    <span>文件大小:</span>
                    <span id="cloudinary-size">-</span>
                </div>
                <div class="metric-item">
                    <span>传输速度:</span>
                    <span id="cloudinary-speed">-</span>
                </div>
            </div>
        </div>

        <div class="video-item">
            <h3>📁 本地文件</h3>
            <video id="local-video" muted loop></video>
            <div class="metrics">
                <div class="metric-item">
                    <span>状态:</span>
                    <span id="local-status" class="status">待测试</span>
                </div>
                <div class="metric-item">
                    <span>加载时间:</span>
                    <span id="local-time">-</span>
                </div>
                <div class="metric-item">
                    <span>文件大小:</span>
                    <span id="local-size">-</span>
                </div>
                <div class="metric-item">
                    <span>传输速度:</span>
                    <span id="local-speed">-</span>
                </div>
            </div>
        </div>
    </div>

    <div class="results" id="results" style="display: none;">
        <h3>📈 测试结果分析</h3>
        <div id="analysis"></div>
    </div>

    <script src="/config.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script>
        let testResults = {
            cloudinary: {},
            local: {}
        };

        async function startTest() {
            console.log('🚀 开始性能测试...');
            clearResults();
            
            // 并行测试两种方式
            await Promise.all([
                testCloudinary(),
                testLocal()
            ]);
            
            showResults();
        }

        async function testCloudinary() {
            const video = document.getElementById('cloudinary-video');
            const statusEl = document.getElementById('cloudinary-status');
            const timeEl = document.getElementById('cloudinary-time');
            const sizeEl = document.getElementById('cloudinary-size');
            const speedEl = document.getElementById('cloudinary-speed');

            statusEl.textContent = '加载中...';
            statusEl.className = 'status loading';

            const startTime = performance.now();

            try {
                // 使用Cloudinary管理器
                if (window.CONFIG?.CLOUDINARY?.ENABLED && window.CloudinaryVideoManager) {
                    const manager = new CloudinaryVideoManager(window.CONFIG.CLOUDINARY.CLOUD_NAME);
                    const success = await manager.loadVideoBackground('home', video);
                    
                    if (success) {
                        const endTime = performance.now();
                        const loadTime = endTime - startTime;
                        
                        testResults.cloudinary = {
                            loadTime: loadTime,
                            success: true,
                            url: video.src
                        };

                        statusEl.textContent = '加载成功';
                        statusEl.className = 'status success';
                        timeEl.textContent = `${loadTime.toFixed(0)}ms`;
                        
                        // 获取文件信息
                        try {
                            const response = await fetch(video.src, { method: 'HEAD' });
                            const contentLength = response.headers.get('content-length');
                            if (contentLength) {
                                const sizeMB = (parseInt(contentLength) / 1024 / 1024).toFixed(2);
                                const speed = (sizeMB / (loadTime / 1000)).toFixed(2);
                                sizeEl.textContent = `${sizeMB}MB`;
                                speedEl.textContent = `${speed}MB/s`;
                                
                                testResults.cloudinary.size = sizeMB;
                                testResults.cloudinary.speed = speed;
                            }
                        } catch (e) {
                            console.log('无法获取Cloudinary文件大小信息');
                        }
                    } else {
                        throw new Error('Cloudinary加载失败');
                    }
                } else {
                    throw new Error('Cloudinary未配置或未启用');
                }
            } catch (error) {
                console.error('Cloudinary测试失败:', error);
                statusEl.textContent = '加载失败';
                statusEl.className = 'status error';
                testResults.cloudinary = { success: false, error: error.message };
            }
        }

        async function testLocal() {
            const video = document.getElementById('local-video');
            const statusEl = document.getElementById('local-status');
            const timeEl = document.getElementById('local-time');
            const sizeEl = document.getElementById('local-size');
            const speedEl = document.getElementById('local-speed');

            statusEl.textContent = '加载中...';
            statusEl.className = 'status loading';

            const startTime = performance.now();

            try {
                const localUrl = '/background/home/<USER>';
                video.src = localUrl;

                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    setTimeout(() => reject(new Error('超时')), 30000);
                });

                const endTime = performance.now();
                const loadTime = endTime - startTime;

                testResults.local = {
                    loadTime: loadTime,
                    success: true,
                    url: localUrl
                };

                statusEl.textContent = '加载成功';
                statusEl.className = 'status success';
                timeEl.textContent = `${loadTime.toFixed(0)}ms`;

                // 获取文件信息
                try {
                    const response = await fetch(localUrl, { method: 'HEAD' });
                    const contentLength = response.headers.get('content-length');
                    if (contentLength) {
                        const sizeMB = (parseInt(contentLength) / 1024 / 1024).toFixed(2);
                        const speed = (sizeMB / (loadTime / 1000)).toFixed(2);
                        sizeEl.textContent = `${sizeMB}MB`;
                        speedEl.textContent = `${speed}MB/s`;
                        
                        testResults.local.size = sizeMB;
                        testResults.local.speed = speed;
                    }
                } catch (e) {
                    console.log('无法获取本地文件大小信息');
                }

            } catch (error) {
                console.error('本地文件测试失败:', error);
                statusEl.textContent = '加载失败';
                statusEl.className = 'status error';
                testResults.local = { success: false, error: error.message };
            }
        }

        function showResults() {
            const resultsEl = document.getElementById('results');
            const analysisEl = document.getElementById('analysis');
            
            let analysis = '<h4>📊 性能分析报告</h4>';
            
            if (testResults.cloudinary.success && testResults.local.success) {
                const timeDiff = testResults.local.loadTime - testResults.cloudinary.loadTime;
                const timeDiffPercent = ((timeDiff / testResults.local.loadTime) * 100).toFixed(1);
                
                if (timeDiff > 0) {
                    analysis += `<p>✅ <strong>Cloudinary比本地文件快 ${timeDiff.toFixed(0)}ms (${timeDiffPercent}%)</strong></p>`;
                } else {
                    analysis += `<p>⚠️ <strong>本地文件比Cloudinary快 ${Math.abs(timeDiff).toFixed(0)}ms</strong></p>`;
                }
                
                if (testResults.cloudinary.size && testResults.local.size) {
                    const sizeDiff = testResults.local.size - testResults.cloudinary.size;
                    const sizeDiffPercent = ((sizeDiff / testResults.local.size) * 100).toFixed(1);
                    
                    if (sizeDiff > 0) {
                        analysis += `<p>💾 <strong>Cloudinary文件小 ${sizeDiff}MB (节省${sizeDiffPercent}%)</strong></p>`;
                    }
                }
                
                analysis += '<h4>🎯 建议</h4>';
                if (timeDiff > 1000) {
                    analysis += '<p>🚀 <strong>强烈建议使用Cloudinary</strong> - 显著提升加载速度</p>';
                } else if (timeDiff > 0) {
                    analysis += '<p>👍 <strong>建议使用Cloudinary</strong> - 有一定性能提升</p>';
                } else {
                    analysis += '<p>🤔 <strong>可考虑继续使用本地文件</strong> - 或检查Cloudinary配置</p>';
                }
            } else {
                analysis += '<p>❌ 测试未完全成功，请检查配置和网络连接</p>';
            }
            
            analysisEl.innerHTML = analysis;
            resultsEl.style.display = 'block';
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
            
            // 重置所有状态
            ['cloudinary', 'local'].forEach(type => {
                document.getElementById(`${type}-status`).textContent = '待测试';
                document.getElementById(`${type}-status`).className = 'status';
                document.getElementById(`${type}-time`).textContent = '-';
                document.getElementById(`${type}-size`).textContent = '-';
                document.getElementById(`${type}-speed`).textContent = '-';
            });
            
            testResults = { cloudinary: {}, local: {} };
        }
    </script>
</body>
</html>
