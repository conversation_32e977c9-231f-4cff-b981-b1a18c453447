<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN监控面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .status-item {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-standby { background: #fff3cd; color: #856404; }
        .status-disabled { background: #f8d7da; color: #721c24; }
        .status-fallback { background: #e2e3e5; color: #383d41; }
        
        .quota-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .quota-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }
        
        .performance-chart {
            height: 200px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        
        .log-panel {
            grid-column: 1 / -1;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            padding: 8px;
            border-left: 4px solid #007bff;
            margin: 5px 0;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-error { border-left-color: #dc3545; }
        .log-warning { border-left-color: #ffc107; }
        .log-success { border-left-color: #28a745; }
        
        .video-test {
            grid-column: 1 / -1;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <h1>🖥️ CDN负载均衡监控面板</h1>
    
    <div class="controls">
        <button onclick="refreshStatus()">🔄 刷新状态</button>
        <button onclick="testAllSources()">🧪 测试所有源</button>
        <button onclick="clearLogs()">🗑️ 清除日志</button>
        <button onclick="exportStats()" class="danger">📊 导出统计</button>
    </div>

    <div class="dashboard">
        <!-- CDN源状态 -->
        <div class="panel">
            <h3>📡 CDN源状态</h3>
            <div id="cdn-sources" class="status-grid">
                <div class="status-item status-active">
                    <strong>加载中...</strong>
                </div>
            </div>
        </div>

        <!-- 配额使用情况 -->
        <div class="panel">
            <h3>📊 配额使用情况</h3>
            <div id="quota-status">
                <p>正在加载配额信息...</p>
            </div>
        </div>

        <!-- 性能统计 -->
        <div class="panel">
            <h3>⚡ 性能统计</h3>
            <div id="performance-stats">
                <div class="performance-chart">
                    <p>正在加载性能数据...</p>
                </div>
            </div>
        </div>

        <!-- 系统配置 -->
        <div class="panel">
            <h3>⚙️ 系统配置</h3>
            <div id="system-config">
                <p>正在加载配置信息...</p>
            </div>
        </div>

        <!-- 视频测试 -->
        <div class="panel video-test">
            <h3>🎬 实时视频测试</h3>
            <div class="controls">
                <select id="page-selector">
                    <option value="home">首页</option>
                    <option value="meetings">相遇回忆</option>
                    <option value="anniversary">纪念日</option>
                    <option value="together-days">在一起的日子</option>
                    <option value="memorial">纪念相册</option>
                </select>
                <button onclick="testVideoLoad()">🎯 测试加载</button>
            </div>
            <div class="video-container">
                <video id="test-video" muted loop controls></video>
            </div>
            <div id="test-results"></div>
        </div>

        <!-- 日志面板 -->
        <div class="panel log-panel">
            <h3>📝 系统日志</h3>
            <div id="log-container">
                <div class="log-entry">系统启动中...</div>
            </div>
        </div>
    </div>

    <script src="/config.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>
    <script src="/simple-video-manager.js"></script>
    
    <script>
        let cdnManager = null;
        let logs = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCDNManager();
            refreshStatus();
            
            // 每30秒自动刷新
            setInterval(refreshStatus, 30000);
        });

        // 初始化CDN管理器
        function initializeCDNManager() {
            if (window.HybridCDNManager) {
                cdnManager = new window.HybridCDNManager();
                addLog('success', '混合CDN管理器已初始化');
            } else if (window.CloudinaryLoadBalancer) {
                cdnManager = new window.CloudinaryLoadBalancer();
                addLog('success', 'Cloudinary负载均衡器已初始化');
            } else {
                addLog('error', '未找到可用的CDN管理器');
            }
        }

        // 刷新状态
        function refreshStatus() {
            updateCDNSources();
            updateQuotaStatus();
            updatePerformanceStats();
            updateSystemConfig();
        }

        // 更新CDN源状态
        function updateCDNSources() {
            const container = document.getElementById('cdn-sources');
            
            if (!cdnManager) {
                container.innerHTML = '<div class="status-item status-disabled"><strong>CDN管理器未初始化</strong></div>';
                return;
            }

            let html = '';
            const status = cdnManager.getSystemStatus ? cdnManager.getSystemStatus() : cdnManager.getStatus();
            
            if (status.sources) {
                status.sources.forEach(source => {
                    const statusClass = `status-${source.status}`;
                    html += `
                        <div class="status-item ${statusClass}">
                            <strong>${source.id}</strong><br>
                            <small>${source.type || 'cloudinary'}</small><br>
                            <small>优先级: ${source.priority || 'N/A'}</small><br>
                            <small>使用: ${source.usage || '0%'}</small>
                        </div>
                    `;
                });
            } else if (status.providers) {
                status.providers.forEach(provider => {
                    const statusClass = `status-${provider.status}`;
                    html += `
                        <div class="status-item ${statusClass}">
                            <strong>${provider.id}</strong><br>
                            <small>权重: ${provider.weight}%</small><br>
                            <small>错误: ${provider.errorCount}</small>
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html || '<div class="status-item status-disabled"><strong>无可用源</strong></div>';
        }

        // 更新配额状态
        function updateQuotaStatus() {
            const container = document.getElementById('quota-status');
            
            if (!cdnManager || !cdnManager.getSystemStatus) {
                container.innerHTML = '<p>配额信息不可用</p>';
                return;
            }

            const status = cdnManager.getSystemStatus();
            let html = '';
            
            status.sources.forEach(source => {
                if (source.quota !== Infinity) {
                    const percentage = (source.used / source.quota * 100).toFixed(1);
                    html += `
                        <div style="margin: 10px 0;">
                            <strong>${source.id}</strong>
                            <div class="quota-bar">
                                <div class="quota-fill" style="width: ${percentage}%"></div>
                            </div>
                            <small>${source.used.toFixed(2)}GB / ${source.quota}GB (${percentage}%)</small>
                        </div>
                    `;
                }
            });
            
            container.innerHTML = html || '<p>无配额限制的源</p>';
        }

        // 更新性能统计
        function updatePerformanceStats() {
            const container = document.getElementById('performance-stats');
            
            if (!cdnManager || !cdnManager.getSystemStatus) {
                container.innerHTML = '<div class="performance-chart"><p>性能数据不可用</p></div>';
                return;
            }

            const status = cdnManager.getSystemStatus();
            let html = '<div class="performance-chart">';
            
            if (status.performance && Object.keys(status.performance).length > 0) {
                html += '<table style="width: 100%; text-align: left;">';
                html += '<tr><th>源</th><th>成功率</th><th>平均加载时间</th><th>尝试次数</th></tr>';
                
                Object.entries(status.performance).forEach(([sourceId, stats]) => {
                    html += `
                        <tr>
                            <td>${sourceId}</td>
                            <td>${(stats.successRate * 100).toFixed(1)}%</td>
                            <td>${stats.avgLoadTime.toFixed(0)}ms</td>
                            <td>${stats.totalAttempts}</td>
                        </tr>
                    `;
                });
                
                html += '</table>';
            } else {
                html += '<p>暂无性能数据</p>';
            }
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 更新系统配置
        function updateSystemConfig() {
            const container = document.getElementById('system-config');
            
            const config = window.CONFIG?.CLOUDINARY;
            let html = '';
            
            if (config) {
                html += `
                    <p><strong>Cloud Name:</strong> ${config.CLOUD_NAME}</p>
                    <p><strong>启用状态:</strong> ${config.ENABLED ? '✅ 已启用' : '❌ 未启用'}</p>
                    <p><strong>策略:</strong> ${cdnManager?.strategy || 'N/A'}</p>
                `;
            }
            
            if (cdnManager) {
                const status = cdnManager.getSystemStatus ? cdnManager.getSystemStatus() : cdnManager.getStatus();
                html += `<p><strong>管理器类型:</strong> ${cdnManager.constructor.name}</p>`;
                html += `<p><strong>可用源数量:</strong> ${status.sources?.length || status.providers?.length || 0}</p>`;
            }
            
            container.innerHTML = html || '<p>配置信息不可用</p>';
        }

        // 测试视频加载
        async function testVideoLoad() {
            const pageSelector = document.getElementById('page-selector');
            const video = document.getElementById('test-video');
            const results = document.getElementById('test-results');
            const pageName = pageSelector.value;
            
            results.innerHTML = '<p>🔄 正在测试加载...</p>';
            addLog('info', `开始测试 ${pageName} 页面视频加载`);
            
            try {
                if (cdnManager && cdnManager.loadVideoBackground) {
                    const result = await cdnManager.loadVideoBackground(pageName, video);
                    
                    if (result.success) {
                        results.innerHTML = `
                            <p>✅ 加载成功</p>
                            <p>源: ${result.source}</p>
                            <p>加载时间: ${result.loadTime.toFixed(0)}ms</p>
                        `;
                        addLog('success', `${pageName} 视频加载成功，来源: ${result.source}`);
                    } else {
                        results.innerHTML = `<p>❌ 加载失败: ${result.error}</p>`;
                        addLog('error', `${pageName} 视频加载失败: ${result.error}`);
                    }
                } else {
                    results.innerHTML = '<p>❌ CDN管理器不可用</p>';
                    addLog('error', 'CDN管理器不可用');
                }
            } catch (error) {
                results.innerHTML = `<p>❌ 测试异常: ${error.message}</p>`;
                addLog('error', `测试异常: ${error.message}`);
            }
        }

        // 测试所有源
        async function testAllSources() {
            addLog('info', '开始测试所有CDN源...');
            
            const pages = ['home', 'meetings', 'anniversary', 'together-days', 'memorial'];
            const testVideo = document.createElement('video');
            
            for (const page of pages) {
                try {
                    if (cdnManager && cdnManager.loadVideoBackground) {
                        const result = await cdnManager.loadVideoBackground(page, testVideo);
                        if (result.success) {
                            addLog('success', `${page}: 加载成功 (${result.source}, ${result.loadTime.toFixed(0)}ms)`);
                        } else {
                            addLog('error', `${page}: 加载失败 (${result.error})`);
                        }
                    }
                } catch (error) {
                    addLog('error', `${page}: 测试异常 (${error.message})`);
                }
                
                // 延迟1秒避免过快请求
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            addLog('info', '所有源测试完成');
            refreshStatus();
        }

        // 添加日志
        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.unshift({ type, message, timestamp });
            
            // 保持最多100条日志
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }
            
            updateLogDisplay();
        }

        // 更新日志显示
        function updateLogDisplay() {
            const container = document.getElementById('log-container');
            let html = '';
            
            logs.forEach(log => {
                html += `<div class="log-entry log-${log.type}">[${log.timestamp}] ${log.message}</div>`;
            });
            
            container.innerHTML = html || '<div class="log-entry">暂无日志</div>';
        }

        // 清除日志
        function clearLogs() {
            logs = [];
            updateLogDisplay();
            addLog('info', '日志已清除');
        }

        // 导出统计
        function exportStats() {
            const status = cdnManager ? (cdnManager.getSystemStatus ? cdnManager.getSystemStatus() : cdnManager.getStatus()) : {};
            const data = {
                timestamp: new Date().toISOString(),
                status: status,
                logs: logs.slice(0, 50), // 导出最近50条日志
                config: window.CONFIG?.CLOUDINARY
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cdn-stats-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addLog('info', '统计数据已导出');
        }
    </script>
</body>
</html>
