<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudinary集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.loading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .config-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Cloudinary集成测试</h1>
    
    <div class="test-container">
        <h2>📋 配置信息</h2>
        <div class="config-info" id="config-info">
            <p>正在加载配置...</p>
        </div>
    </div>

    <div class="test-container">
        <h2>🎬 视频加载测试</h2>
        <div class="status" id="status">待测试</div>
        
        <div class="video-container">
            <video id="test-video" muted loop controls></video>
        </div>
        
        <button onclick="testCloudinaryLoad()">测试Cloudinary加载</button>
        <button onclick="testLocalLoad()">测试本地文件加载</button>
        <button onclick="clearTest()">清除测试</button>
    </div>

    <div class="test-container">
        <h2>📊 测试结果</h2>
        <div id="results"></div>
    </div>

    <script src="/config.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script>
        // 显示配置信息
        function showConfig() {
            const configInfo = document.getElementById('config-info');
            const config = window.CONFIG?.CLOUDINARY;
            
            if (config) {
                configInfo.innerHTML = `
                    <p><strong>Cloud Name:</strong> ${config.CLOUD_NAME}</p>
                    <p><strong>启用状态:</strong> ${config.ENABLED ? '✅ 已启用' : '❌ 未启用'}</p>
                    <p><strong>Base URL:</strong> ${config.BASE_URL}</p>
                    <p><strong>默认变换:</strong> ${config.TRANSFORMATIONS.DEFAULT}</p>
                `;
            } else {
                configInfo.innerHTML = '<p>❌ 配置未加载</p>';
            }
        }

        // 测试Cloudinary加载
        async function testCloudinaryLoad() {
            const video = document.getElementById('test-video');
            const status = document.getElementById('status');
            
            status.textContent = '正在测试Cloudinary加载...';
            status.className = 'status loading';
            
            try {
                if (!window.CloudinaryVideoManager) {
                    throw new Error('CloudinaryVideoManager未加载');
                }
                
                const manager = new CloudinaryVideoManager(window.CONFIG.CLOUDINARY.CLOUD_NAME);
                const success = await manager.loadVideoBackground('home', video);
                
                if (success) {
                    status.textContent = '✅ Cloudinary加载成功';
                    status.className = 'status success';
                    addResult('Cloudinary', '成功', video.src);
                } else {
                    throw new Error('Cloudinary加载失败');
                }
                
            } catch (error) {
                console.error('Cloudinary测试失败:', error);
                status.textContent = `❌ Cloudinary加载失败: ${error.message}`;
                status.className = 'status error';
                addResult('Cloudinary', '失败', error.message);
            }
        }

        // 测试本地文件加载
        async function testLocalLoad() {
            const video = document.getElementById('test-video');
            const status = document.getElementById('status');
            
            status.textContent = '正在测试本地文件加载...';
            status.className = 'status loading';
            
            try {
                video.src = '/background/home/<USER>';
                
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    setTimeout(() => reject(new Error('加载超时')), 10000);
                });
                
                status.textContent = '✅ 本地文件加载成功';
                status.className = 'status success';
                addResult('本地文件', '成功', video.src);
                
            } catch (error) {
                console.error('本地文件测试失败:', error);
                status.textContent = `❌ 本地文件加载失败: ${error.message}`;
                status.className = 'status error';
                addResult('本地文件', '失败', error.message);
            }
        }

        // 清除测试
        function clearTest() {
            const video = document.getElementById('test-video');
            const status = document.getElementById('status');
            
            video.src = '';
            status.textContent = '待测试';
            status.className = 'status';
        }

        // 添加测试结果
        function addResult(type, result, details) {
            const results = document.getElementById('results');
            const time = new Date().toLocaleTimeString();
            
            results.innerHTML += `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                    <strong>${time}</strong> - ${type}: ${result}<br>
                    <small>${details}</small>
                </div>
            `;
        }

        // 页面加载时显示配置
        document.addEventListener('DOMContentLoaded', showConfig);
    </script>
</body>
</html>
